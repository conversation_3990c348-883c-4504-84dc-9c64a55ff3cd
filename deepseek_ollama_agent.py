#!/usr/bin/env python3
"""
基于Ollama + DeepSeek的轻量级智能体
更简单的部署方案，适合快速开始
"""

import json
import re
import requests
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import subprocess
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OllamaDeepSeekAgent:
    """基于Ollama的DeepSeek智能体"""
    
    def __init__(self, model_name="deepseek-coder:6.7b", ollama_url="http://localhost:11434"):
        """
        初始化智能体
        
        Args:
            model_name: Ollama中的DeepSeek模型名称
            ollama_url: Ollama服务地址
        """
        self.model_name = model_name
        self.ollama_url = ollama_url
        self.tools = {}
        self.memory = []
        self.max_memory_length = 10
        
        # 检查Ollama服务
        self._check_ollama_service()
        
        # 注册默认工具
        self._register_default_tools()
    
    def _check_ollama_service(self):
        """检查Ollama服务是否运行"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [model["name"] for model in models]
                
                if self.model_name not in model_names:
                    logger.warning(f"模型 {self.model_name} 未找到")
                    logger.info("可用模型:", model_names)
                    
                    # 尝试拉取模型
                    self._pull_model()
                else:
                    logger.info(f"模型 {self.model_name} 已就绪")
            else:
                raise Exception("Ollama服务响应异常")
                
        except requests.exceptions.RequestException:
            logger.error("无法连接到Ollama服务")
            logger.info("请确保Ollama已启动: ollama serve")
            raise
    
    def _pull_model(self):
        """拉取DeepSeek模型"""
        logger.info(f"正在拉取模型 {self.model_name}...")
        try:
            # 使用subprocess调用ollama pull
            result = subprocess.run(
                ["ollama", "pull", self.model_name],
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            if result.returncode == 0:
                logger.info("模型拉取成功！")
            else:
                logger.error(f"模型拉取失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error("模型拉取超时")
        except FileNotFoundError:
            logger.error("未找到ollama命令，请确保Ollama已正确安装")
    
    def _register_default_tools(self):
        """注册默认工具"""
        
        def search_web(query: str) -> str:
            """网络搜索工具（模拟）"""
            return f"搜索 '{query}' 的结果：这是一个模拟的搜索结果，包含与 {query} 相关的信息。"
        
        def calculate(expression: str) -> str:
            """安全计算器"""
            try:
                # 只允许基本数学运算
                allowed_chars = set('0123456789+-*/.() ')
                if all(c in allowed_chars for c in expression):
                    result = eval(expression)
                    return f"{expression} = {result}"
                else:
                    return "错误：表达式包含不允许的字符"
            except Exception as e:
                return f"计算错误: {e}"
        
        def get_time() -> str:
            """获取当前时间"""
            return datetime.now().strftime("当前时间：%Y年%m月%d日 %H:%M:%S")
        
        def write_note(content: str) -> str:
            """写笔记"""
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"note_{timestamp}.txt"
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"创建时间: {datetime.now()}\n")
                    f.write(f"内容: {content}\n")
                return f"笔记已保存到 {filename}"
            except Exception as e:
                return f"保存笔记失败: {e}"
        
        def list_files() -> str:
            """列出当前目录文件"""
            try:
                files = os.listdir('.')
                return "当前目录文件:\n" + "\n".join(f"- {f}" for f in files[:20])
            except Exception as e:
                return f"列出文件失败: {e}"
        
        # 注册工具
        self.tools = {
            "search": {"func": search_web, "desc": "搜索信息"},
            "calc": {"func": calculate, "desc": "数学计算"},
            "time": {"func": get_time, "desc": "获取当前时间"},
            "note": {"func": write_note, "desc": "写笔记"},
            "ls": {"func": list_files, "desc": "列出文件"}
        }
    
    def _call_ollama(self, prompt: str, system_prompt: str = "") -> str:
        """调用Ollama API"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "system": system_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9
                }
            }
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()["response"]
            else:
                return f"API调用失败: {response.status_code}"
                
        except requests.exceptions.RequestException as e:
            return f"网络请求失败: {e}"
    
    def _parse_tool_usage(self, text: str) -> Optional[Dict[str, str]]:
        """解析工具使用指令"""
        # 匹配模式: [工具名:参数] 或 使用工具 工具名 参数
        patterns = [
            r'\[(\w+):([^\]]+)\]',
            r'使用工具\s+(\w+)\s+(.+)',
            r'调用\s+(\w+)\s+(.+)',
            r'执行\s+(\w+)\s+(.+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                tool_name = match.group(1).strip()
                params = match.group(2).strip()
                return {"tool": tool_name, "params": params}
        
        return None
    
    def _execute_tool(self, tool_name: str, params: str) -> str:
        """执行工具"""
        if tool_name not in self.tools:
            available_tools = ", ".join(self.tools.keys())
            return f"工具 '{tool_name}' 不存在。可用工具: {available_tools}"
        
        try:
            result = self.tools[tool_name]["func"](params)
            return str(result)
        except Exception as e:
            return f"工具执行错误: {e}"
    
    def _get_tools_info(self) -> str:
        """获取工具信息"""
        info = "可用工具:\n"
        for name, tool in self.tools.items():
            info += f"- {name}: {tool['desc']}\n"
        info += "\n使用方法: [工具名:参数] 例如: [calc:2+3] 或 [search:Python教程]"
        return info
    
    def process_message(self, user_input: str) -> str:
        """处理用户消息"""
        try:
            # 构建系统提示
            system_prompt = f"""你是一个智能助手，可以使用工具来帮助用户。

{self._get_tools_info()}

当用户需要使用工具时，请在回复中包含 [工具名:参数] 的格式。
例如：[calc:2+3] 或 [search:Python教程]

请自然地与用户对话，在需要时主动使用工具。"""
            
            # 添加对话历史
            context = ""
            if self.memory:
                context = "最近的对话:\n"
                for item in self.memory[-3:]:
                    context += f"用户: {item['user']}\n助手: {item['assistant']}\n\n"
            
            full_prompt = f"{context}用户: {user_input}\n助手: "
            
            # 调用模型
            response = self._call_ollama(full_prompt, system_prompt)
            
            # 检查是否需要使用工具
            tool_usage = self._parse_tool_usage(response)
            
            if tool_usage:
                # 执行工具
                tool_result = self._execute_tool(tool_usage["tool"], tool_usage["params"])
                
                # 基于工具结果生成最终回复
                final_prompt = f"""用户问题: {user_input}
我的初始回复: {response}
工具执行结果: {tool_result}

请基于工具执行结果，给出完整的最终回复:"""
                
                final_response = self._call_ollama(final_prompt, system_prompt)
                
                # 保存到记忆
                self._add_to_memory(user_input, final_response)
                
                return final_response
            else:
                # 直接回复
                self._add_to_memory(user_input, response)
                return response
                
        except Exception as e:
            return f"处理消息时发生错误: {e}"
    
    def _add_to_memory(self, user_input: str, response: str):
        """添加到对话记忆"""
        self.memory.append({
            "user": user_input,
            "assistant": response,
            "timestamp": datetime.now().isoformat()
        })
        
        # 保持记忆长度
        if len(self.memory) > self.max_memory_length:
            self.memory.pop(0)
    
    def chat(self):
        """开始对话"""
        print("🤖 DeepSeek智能体已启动！")
        print("💡 输入 'help' 查看帮助，输入 'quit' 退出")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n👤 用户: ").strip()
                
                if user_input.lower() == 'quit':
                    print("👋 再见！")
                    break
                elif user_input.lower() == 'help':
                    print(self._get_tools_info())
                    continue
                elif not user_input:
                    continue
                
                print("🤔 AI正在思考...")
                response = self.process_message(user_input)
                print(f"🤖 AI: {response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    print("🚀 正在启动DeepSeek智能体...")
    
    try:
        # 创建智能体
        agent = OllamaDeepSeekAgent()
        
        # 开始对话
        agent.chat()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("📋 请检查:")
        print("1. Ollama是否已安装并运行 (ollama serve)")
        print("2. DeepSeek模型是否已下载 (ollama pull deepseek-coder:6.7b)")

if __name__ == "__main__":
    main()
