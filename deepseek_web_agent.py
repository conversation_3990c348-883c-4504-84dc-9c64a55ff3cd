#!/usr/bin/env python3
"""
基于Streamlit的DeepSeek智能体Web界面
提供友好的图形化交互界面
"""

import streamlit as st
import os
from datetime import datetime
import json

from deepseek_api_agent import DeepSeekAPIAgent

# 页面配置
st.set_page_config(
    page_title="DeepSeek智能体",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}
.chat-message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}
.user-message {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}
.assistant-message {
    background-color: #f3e5f5;
    border-left: 4px solid #9c27b0;
}
.tool-info {
    background-color: #fff3e0;
    border: 1px solid #ff9800;
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin: 0.5rem 0;
}
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """初始化会话状态"""
    if 'agent' not in st.session_state:
        st.session_state.agent = None
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    if 'api_key_set' not in st.session_state:
        st.session_state.api_key_set = False

def setup_sidebar():
    """设置侧边栏"""
    with st.sidebar:
        st.header("🔧 配置")
        
        # API密钥输入
        api_key = st.text_input(
            "DeepSeek API密钥",
            type="password",
            value=os.getenv("DEEPSEEK_API_KEY", ""),
            help="请输入您的DeepSeek API密钥"
        )
        
        # 模型选择
        model = st.selectbox(
            "选择模型",
            ["deepseek-chat", "deepseek-coder"],
            help="选择要使用的DeepSeek模型"
        )
        
        # 初始化智能体按钮
        if st.button("🚀 初始化智能体", type="primary"):
            if api_key:
                try:
                    os.environ["DEEPSEEK_API_KEY"] = api_key
                    st.session_state.agent = DeepSeekAPIAgent(api_key=api_key, model=model)
                    st.session_state.api_key_set = True
                    st.success("✅ 智能体初始化成功！")
                except Exception as e:
                    st.error(f"❌ 初始化失败: {e}")
            else:
                st.error("❌ 请输入API密钥")
        
        st.divider()
        
        # 工具信息
        if st.session_state.agent:
            st.header("🛠️ 可用工具")
            tools_info = st.session_state.agent.get_tools_info()
            st.text_area("工具列表", tools_info, height=200, disabled=True)
        
        st.divider()
        
        # 对话管理
        st.header("💬 对话管理")
        if st.button("🗑️ 清空对话历史"):
            st.session_state.chat_history = []
            if st.session_state.agent:
                st.session_state.agent.clear_history()
            st.success("✅ 对话历史已清空")
        
        # 导出对话
        if st.session_state.chat_history and st.button("📥 导出对话"):
            chat_data = {
                "export_time": datetime.now().isoformat(),
                "chat_history": st.session_state.chat_history
            }
            st.download_button(
                label="下载对话记录",
                data=json.dumps(chat_data, ensure_ascii=False, indent=2),
                file_name=f"chat_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )

def display_chat_history():
    """显示对话历史"""
    for i, message in enumerate(st.session_state.chat_history):
        if message["role"] == "user":
            st.markdown(f"""
            <div class="chat-message user-message">
                <strong>👤 用户:</strong><br>
                {message["content"]}
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div class="chat-message assistant-message">
                <strong>🤖 AI助手:</strong><br>
                {message["content"]}
            </div>
            """, unsafe_allow_html=True)

def main():
    """主函数"""
    # 初始化会话状态
    initialize_session_state()
    
    # 设置侧边栏
    setup_sidebar()
    
    # 主标题
    st.markdown('<h1 class="main-header">🤖 DeepSeek智能体</h1>', unsafe_allow_html=True)
    
    # 检查智能体是否已初始化
    if not st.session_state.api_key_set or not st.session_state.agent:
        st.warning("⚠️ 请在左侧边栏配置API密钥并初始化智能体")
        
        # 显示使用说明
        st.markdown("""
        ## 📖 使用说明
        
        1. **获取API密钥**: 访问 [DeepSeek平台](https://platform.deepseek.com/) 注册并获取API密钥
        2. **配置密钥**: 在左侧边栏输入您的API密钥
        3. **选择模型**: 选择适合的模型（deepseek-chat 或 deepseek-coder）
        4. **初始化智能体**: 点击"初始化智能体"按钮
        5. **开始对话**: 在下方输入框中输入您的问题
        
        ## 🛠️ 功能特性
        
        - **智能对话**: 基于DeepSeek大模型的自然语言理解
        - **工具调用**: 自动调用计算器、天气查询、搜索等工具
        - **记忆系统**: 保持对话上下文和历史记录
        - **Web界面**: 友好的图形化交互界面
        - **对话管理**: 支持清空历史、导出对话记录
        """)
        return
    
    # 显示对话历史
    if st.session_state.chat_history:
        st.subheader("💬 对话历史")
        display_chat_history()
    
    # 用户输入
    st.subheader("💭 输入您的问题")
    
    # 创建输入表单
    with st.form("chat_form", clear_on_submit=True):
        user_input = st.text_area(
            "请输入您的问题:",
            height=100,
            placeholder="例如: 帮我计算 (100 + 200) * 3，或者 查询北京的天气"
        )
        
        col1, col2, col3 = st.columns([1, 1, 4])
        
        with col1:
            submit_button = st.form_submit_button("🚀 发送", type="primary")
        
        with col2:
            example_button = st.form_submit_button("💡 示例")
    
    # 处理示例按钮
    if example_button:
        examples = [
            "帮我计算 (25 + 75) * 2",
            "现在几点了？",
            "查询北京的天气",
            "搜索Python编程教程",
            "帮我保存一个文件，内容是'Hello World'"
        ]
        st.info("💡 示例问题:\n" + "\n".join([f"- {ex}" for ex in examples]))
    
    # 处理用户输入
    if submit_button and user_input.strip():
        # 添加用户消息到历史
        st.session_state.chat_history.append({
            "role": "user",
            "content": user_input,
            "timestamp": datetime.now().isoformat()
        })
        
        # 显示处理状态
        with st.spinner("🤔 AI正在思考..."):
            try:
                # 调用智能体处理消息
                response = st.session_state.agent.process_message(user_input)
                
                # 添加AI回复到历史
                st.session_state.chat_history.append({
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat()
                })
                
                # 重新运行以显示新消息
                st.rerun()
                
            except Exception as e:
                st.error(f"❌ 处理消息时发生错误: {e}")
    
    # 页脚信息
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666;">
        <p>🤖 基于DeepSeek API的智能体 | 
        <a href="https://platform.deepseek.com/" target="_blank">获取API密钥</a> | 
        <a href="https://github.com/deepseek-ai" target="_blank">DeepSeek GitHub</a></p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
