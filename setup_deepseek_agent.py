#!/usr/bin/env python3
"""
DeepSeek智能体一键安装和启动脚本
"""

import subprocess
import sys
import os
import platform
import time

def run_command(command, description=""):
    """运行命令并显示结果"""
    print(f"🔄 {description}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout:
                print(f"输出: {result.stdout}")
        else:
            print(f"❌ {description} 失败")
            print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    
    return True

def check_python():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor} 符合要求")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.8或更高版本")
        return False

def install_ollama():
    """安装Ollama"""
    print("📦 安装Ollama...")
    
    system = platform.system().lower()
    
    if system == "windows":
        print("Windows系统，请手动下载安装Ollama:")
        print("https://ollama.ai/download/windows")
        input("安装完成后按回车继续...")
    elif system == "darwin":  # macOS
        run_command("curl -fsSL https://ollama.ai/install.sh | sh", "安装Ollama (macOS)")
    elif system == "linux":
        run_command("curl -fsSL https://ollama.ai/install.sh | sh", "安装Ollama (Linux)")
    else:
        print(f"❌ 不支持的操作系统: {system}")
        return False
    
    return True

def start_ollama():
    """启动Ollama服务"""
    print("🚀 启动Ollama服务...")
    
    # 检查Ollama是否已运行
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=3)
        if response.status_code == 200:
            print("✅ Ollama服务已在运行")
            return True
    except:
        pass
    
    # 启动Ollama服务
    system = platform.system().lower()
    
    if system == "windows":
        print("请在新的命令行窗口中运行: ollama serve")
        input("启动完成后按回车继续...")
    else:
        print("在后台启动Ollama服务...")
        subprocess.Popen(["ollama", "serve"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        time.sleep(3)
    
    return True

def pull_deepseek_model():
    """拉取DeepSeek模型"""
    print("📥 拉取DeepSeek模型...")
    
    models_to_try = [
        "deepseek-coder:6.7b",
        "deepseek-coder:1.3b",
        "deepseek-v2:16b"
    ]
    
    for model in models_to_try:
        print(f"尝试拉取模型: {model}")
        if run_command(f"ollama pull {model}", f"拉取 {model}"):
            print(f"✅ 成功拉取模型: {model}")
            return model
        else:
            print(f"❌ 拉取 {model} 失败，尝试下一个...")
    
    print("❌ 所有模型拉取失败")
    return None

def install_python_dependencies():
    """安装Python依赖"""
    print("📦 安装Python依赖...")
    
    dependencies = [
        "requests",
        "transformers",
        "torch",
        "langchain",
        "langchain-community",
        "fastapi",
        "uvicorn"
    ]
    
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"安装 {dep}"):
            print(f"⚠️ {dep} 安装失败，但继续...")
    
    return True

def create_requirements_file():
    """创建requirements.txt文件"""
    requirements = """requests>=2.28.0
transformers>=4.21.0
torch>=1.12.0
langchain>=0.1.0
langchain-community>=0.0.1
fastapi>=0.68.0
uvicorn>=0.15.0
"""
    
    with open("requirements.txt", "w") as f:
        f.write(requirements)
    
    print("✅ 创建 requirements.txt 文件")

def main():
    """主安装流程"""
    print("🎯 DeepSeek智能体安装向导")
    print("=" * 50)
    
    # 1. 检查Python版本
    if not check_python():
        return
    
    # 2. 创建requirements文件
    create_requirements_file()
    
    # 3. 安装Python依赖
    install_python_dependencies()
    
    # 4. 安装Ollama
    print("\n" + "=" * 50)
    install_ollama()
    
    # 5. 启动Ollama服务
    print("\n" + "=" * 50)
    start_ollama()
    
    # 6. 拉取DeepSeek模型
    print("\n" + "=" * 50)
    model_name = pull_deepseek_model()
    
    if model_name:
        print("\n" + "=" * 50)
        print("🎉 安装完成！")
        print("\n启动方式:")
        print("1. 简单版本 (推荐): python deepseek_ollama_agent.py")
        print("2. 完整版本: python deepseek_agent.py")
        print("\n使用的模型:", model_name)
        
        # 询问是否立即启动
        choice = input("\n是否立即启动智能体？(y/n): ").lower()
        if choice == 'y':
            print("🚀 启动智能体...")
            try:
                subprocess.run([sys.executable, "deepseek_ollama_agent.py"])
            except KeyboardInterrupt:
                print("\n👋 再见！")
    else:
        print("\n❌ 安装失败，请检查网络连接和系统配置")

if __name__ == "__main__":
    main()
