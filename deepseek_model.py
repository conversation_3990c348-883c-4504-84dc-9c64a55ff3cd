#!/usr/bin/env python3
"""
DeepSeek模型本地部署脚本
支持DeepSeek-Coder和DeepSeek-V2模型
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DeepSeekModel:
    def __init__(self, model_name="deepseek-ai/deepseek-coder-6.7b-instruct", device="auto"):
        """
        初始化DeepSeek模型
        
        Args:
            model_name: 模型名称，可选：
                - deepseek-ai/deepseek-coder-6.7b-instruct
                - deepseek-ai/deepseek-v2-lite-chat
                - deepseek-ai/deepseek-v2-chat
            device: 设备选择，auto为自动选择
        """
        self.model_name = model_name
        self.device = device
        self.tokenizer = None
        self.model = None
        
        self._load_model()
    
    def _load_model(self):
        """加载模型和分词器"""
        try:
            logger.info(f"正在加载模型: {self.model_name}")
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True
            )
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16,  # 使用半精度节省显存
                device_map=self.device,
                trust_remote_code=True,
                load_in_8bit=True  # 8bit量化节省显存
            )
            
            logger.info("模型加载成功！")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def generate_response(self, prompt, max_length=2048, temperature=0.7):
        """
        生成回复
        
        Args:
            prompt: 输入提示
            max_length: 最大生成长度
            temperature: 温度参数
        
        Returns:
            生成的文本
        """
        try:
            # 编码输入
            inputs = self.tokenizer.encode(prompt, return_tensors="pt")
            inputs = inputs.to(self.model.device)
            
            # 生成回复
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=max_length,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码输出
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # 移除输入部分，只返回生成的内容
            response = response[len(prompt):].strip()
            
            return response
            
        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return f"错误: {e}"
    
    def chat(self, message, system_prompt="你是一个有用的AI助手。"):
        """
        对话接口
        
        Args:
            message: 用户消息
            system_prompt: 系统提示
        
        Returns:
            AI回复
        """
        # 构建对话格式
        prompt = f"System: {system_prompt}\nUser: {message}\nAssistant: "
        
        return self.generate_response(prompt)

def main():
    """测试函数"""
    # 初始化模型
    model = DeepSeekModel()
    
    print("DeepSeek模型已加载，开始对话测试...")
    print("输入 'quit' 退出")
    
    while True:
        user_input = input("\n用户: ")
        if user_input.lower() == 'quit':
            break
        
        response = model.chat(user_input)
        print(f"AI: {response}")

if __name__ == "__main__":
    main()
