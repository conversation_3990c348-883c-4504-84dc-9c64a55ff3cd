#!/usr/bin/env python3
"""
DeepSeek API客户端
支持OpenAI兼容的API调用
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional, Generator
from openai import OpenAI
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DeepSeekAPIClient:
    """DeepSeek API客户端类"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: str = "https://api.deepseek.com"):
        """
        初始化DeepSeek API客户端
        
        Args:
            api_key: DeepSeek API密钥，如果为None则从环境变量DEEPSEEK_API_KEY读取
            base_url: API基础URL
        """
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        if not self.api_key:
            raise ValueError("请设置DEEPSEEK_API_KEY环境变量或传入api_key参数")
        
        self.base_url = base_url
        
        # 初始化OpenAI客户端（DeepSeek兼容OpenAI API）
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        logger.info("DeepSeek API客户端初始化成功")
    
    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "deepseek-chat",
        temperature: float = 0.7,
        max_tokens: int = 2048,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        聊天补全API调用
        
        Args:
            messages: 消息列表，格式：[{"role": "user", "content": "hello"}]
            model: 模型名称，可选：deepseek-chat, deepseek-coder
            temperature: 温度参数 (0-2)
            max_tokens: 最大token数
            stream: 是否流式输出
            **kwargs: 其他参数
        
        Returns:
            API响应结果
        """
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream,
                **kwargs
            )
            
            if stream:
                return response  # 返回生成器对象
            else:
                return {
                    "content": response.choices[0].message.content,
                    "usage": response.usage.dict() if response.usage else None,
                    "model": response.model,
                    "finish_reason": response.choices[0].finish_reason
                }
                
        except Exception as e:
            logger.error(f"API调用失败: {e}")
            raise
    
    def chat_completion_stream(
        self,
        messages: List[Dict[str, str]],
        model: str = "deepseek-chat",
        temperature: float = 0.7,
        max_tokens: int = 2048,
        **kwargs
    ) -> Generator[str, None, None]:
        """
        流式聊天补全
        
        Args:
            messages: 消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
        
        Yields:
            逐步生成的文本内容
        """
        try:
            stream = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True,
                **kwargs
            )
            
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"流式API调用失败: {e}")
            raise
    
    def simple_chat(self, user_message: str, system_prompt: str = "", **kwargs) -> str:
        """
        简单聊天接口
        
        Args:
            user_message: 用户消息
            system_prompt: 系统提示
            **kwargs: 其他参数
        
        Returns:
            AI回复内容
        """
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        messages.append({"role": "user", "content": user_message})
        
        response = self.chat_completion(messages, **kwargs)
        return response["content"]
    
    def get_available_models(self) -> List[str]:
        """
        获取可用模型列表
        
        Returns:
            模型名称列表
        """
        try:
            models = self.client.models.list()
            return [model.id for model in models.data]
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return ["deepseek-chat", "deepseek-coder"]  # 返回默认模型
    
    def count_tokens(self, text: str) -> int:
        """
        估算token数量（简单实现）
        
        Args:
            text: 文本内容
        
        Returns:
            估算的token数量
        """
        # 简单估算：中文字符约1.5个token，英文单词约1个token
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        english_words = len(text.replace(' ', '').replace('\n', '')) - chinese_chars
        
        return int(chinese_chars * 1.5 + english_words * 0.75)

def test_api_client():
    """测试API客户端"""
    try:
        # 初始化客户端
        client = DeepSeekAPIClient()
        
        # 测试简单聊天
        response = client.simple_chat(
            user_message="你好，请介绍一下你自己",
            system_prompt="你是一个有用的AI助手"
        )
        
        print("API测试成功！")
        print(f"回复: {response}")
        
        # 测试可用模型
        models = client.get_available_models()
        print(f"可用模型: {models}")
        
    except Exception as e:
        print(f"API测试失败: {e}")
        print("请检查:")
        print("1. DEEPSEEK_API_KEY环境变量是否设置正确")
        print("2. 网络连接是否正常")
        print("3. API密钥是否有效")

if __name__ == "__main__":
    test_api_client()
