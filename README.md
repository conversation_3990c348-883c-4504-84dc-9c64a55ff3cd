# DeepSeek智能体Agent部署指南

基于DeepSeek开源大模型的本地智能体Agent实现，支持工具调用、记忆系统和任务执行。

## 🚀 快速开始

### 方法1: 一键安装（推荐）

```bash
python setup_deepseek_agent.py
```

### 方法2: 手动安装

#### 1. 安装Ollama

**Windows:**
- 下载并安装: https://ollama.ai/download/windows

**macOS/Linux:**
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

#### 2. 启动Ollama服务

```bash
ollama serve
```

#### 3. 拉取DeepSeek模型

```bash
# 推荐：代码专用模型
ollama pull deepseek-coder:6.7b

# 或者：通用对话模型
ollama pull deepseek-v2:16b

# 轻量版本（资源有限时）
ollama pull deepseek-coder:1.3b
```

#### 4. 安装Python依赖

```bash
pip install -r requirements.txt
```

#### 5. 启动智能体

```bash
# 简单版本（推荐）
python deepseek_ollama_agent.py

# 完整版本（需要更多GPU内存）
python deepseek_agent.py
```

## 📋 系统要求

### 最低配置
- **CPU**: 4核心以上
- **内存**: 16GB
- **存储**: 20GB可用空间
- **Python**: 3.8+

### 推荐配置
- **GPU**: RTX 4060/4070或更高
- **内存**: 32GB
- **存储**: 50GB可用空间

### 模型大小参考
- `deepseek-coder:1.3b` - 约1GB
- `deepseek-coder:6.7b` - 约4GB  
- `deepseek-v2:16b` - 约9GB

## 🛠️ 功能特性

### 内置工具
- **搜索工具**: 网络信息搜索（可扩展真实API）
- **计算器**: 安全的数学表达式计算
- **时间工具**: 获取当前时间
- **文件操作**: 读写文件、列出目录
- **笔记功能**: 快速记录和保存信息

### 使用示例

```
用户: 帮我计算 (25 + 75) * 2
AI: [calc:(25 + 75) * 2]
计算结果: (25 + 75) * 2 = 200

用户: 现在几点了？
AI: [time:]
当前时间：2024年12月19日 14:30:25

用户: 帮我搜索Python教程
AI: [search:Python教程]
搜索结果包含了丰富的Python学习资源...
```

## 🔧 自定义工具

### 添加新工具

在 `deepseek_ollama_agent.py` 中添加：

```python
def my_custom_tool(params: str) -> str:
    """自定义工具功能"""
    # 实现你的工具逻辑
    return f"处理结果: {params}"

# 在 _register_default_tools 方法中注册
self.tools["mytool"] = {
    "func": my_custom_tool, 
    "desc": "我的自定义工具"
}
```

### 集成外部API

```python
def weather_tool(city: str) -> str:
    """天气查询工具"""
    # 集成真实的天气API
    api_key = "your_api_key"
    url = f"http://api.weather.com/v1/current?key={api_key}&q={city}"
    response = requests.get(url)
    return response.json()
```

## 📊 性能优化

### GPU加速
```python
# 在deepseek_model.py中启用GPU
device_map="auto"  # 自动分配GPU
torch_dtype=torch.float16  # 半精度
load_in_8bit=True  # 8bit量化
```

### 内存优化
```python
# 减少上下文长度
max_length=1024  # 降低最大生成长度

# 限制对话历史
max_memory_length=5  # 减少记忆条数
```

## 🐛 常见问题

### Q: Ollama连接失败
**A**: 确保Ollama服务正在运行
```bash
ollama serve
```

### Q: 模型下载慢
**A**: 使用国内镜像或选择较小的模型
```bash
ollama pull deepseek-coder:1.3b  # 选择小模型
```

### Q: GPU内存不足
**A**: 使用CPU模式或量化
```python
device_map="cpu"  # 强制使用CPU
load_in_4bit=True  # 4bit量化
```

### Q: 工具调用不生效
**A**: 检查工具调用格式
```
正确: [calc:2+3]
错误: calc:2+3 或 [calc 2+3]
```

## 🔄 更新和维护

### 更新模型
```bash
ollama pull deepseek-coder:latest
```

### 更新代码
```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

## 📚 扩展阅读

- [Ollama官方文档](https://ollama.ai/docs)
- [DeepSeek模型介绍](https://github.com/deepseek-ai)
- [LangChain工具集成](https://python.langchain.com/docs/modules/agents/tools/)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License - 详见LICENSE文件

---

**享受你的DeepSeek智能体之旅！** 🎉
