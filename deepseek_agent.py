#!/usr/bin/env python3
"""
基于DeepSeek的智能体Agent实现
集成工具调用、记忆系统和任务执行能力
"""

import json
import re
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import requests
import os

from deepseek_model import DeepSeekModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Tool:
    """工具基类"""
    def __init__(self, name: str, description: str, func):
        self.name = name
        self.description = description
        self.func = func
    
    def execute(self, *args, **kwargs):
        """执行工具"""
        try:
            return self.func(*args, **kwargs)
        except Exception as e:
            return f"工具执行错误: {e}"

class DeepSeekAgent:
    """基于DeepSeek的智能体"""
    
    def __init__(self, model_name="deepseek-ai/deepseek-coder-6.7b-instruct"):
        """
        初始化智能体
        
        Args:
            model_name: DeepSeek模型名称
        """
        self.model = DeepSeekModel(model_name)
        self.tools = {}
        self.memory = []
        self.max_memory_length = 10
        
        # 注册默认工具
        self._register_default_tools()
    
    def _register_default_tools(self):
        """注册默认工具"""
        
        def search_web(query: str) -> str:
            """网络搜索工具"""
            try:
                # 这里可以集成真实的搜索API
                return f"搜索结果: {query} - 这是一个模拟的搜索结果"
            except Exception as e:
                return f"搜索失败: {e}"
        
        def calculate(expression: str) -> str:
            """计算器工具"""
            try:
                # 安全的数学表达式计算
                allowed_chars = set('0123456789+-*/.() ')
                if all(c in allowed_chars for c in expression):
                    result = eval(expression)
                    return f"计算结果: {result}"
                else:
                    return "错误: 包含不允许的字符"
            except Exception as e:
                return f"计算错误: {e}"
        
        def get_current_time() -> str:
            """获取当前时间"""
            return f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        def write_file(filename: str, content: str) -> str:
            """写入文件"""
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                return f"文件 {filename} 写入成功"
            except Exception as e:
                return f"文件写入失败: {e}"
        
        def read_file(filename: str) -> str:
            """读取文件"""
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                return f"文件内容:\n{content}"
            except Exception as e:
                return f"文件读取失败: {e}"
        
        # 注册工具
        self.register_tool("search_web", "网络搜索工具，用于搜索信息", search_web)
        self.register_tool("calculate", "计算器工具，用于数学计算", calculate)
        self.register_tool("get_current_time", "获取当前时间", get_current_time)
        self.register_tool("write_file", "写入文件工具", write_file)
        self.register_tool("read_file", "读取文件工具", read_file)
    
    def register_tool(self, name: str, description: str, func):
        """注册新工具"""
        self.tools[name] = Tool(name, description, func)
        logger.info(f"工具 '{name}' 注册成功")
    
    def _get_tools_description(self) -> str:
        """获取工具描述"""
        if not self.tools:
            return "当前没有可用工具。"
        
        tools_desc = "可用工具:\n"
        for tool in self.tools.values():
            tools_desc += f"- {tool.name}: {tool.description}\n"
        
        return tools_desc
    
    def _parse_tool_call(self, response: str) -> Optional[Dict[str, Any]]:
        """解析工具调用"""
        # 查找工具调用模式
        pattern = r"使用工具:\s*(\w+)\s*参数:\s*(.+)"
        match = re.search(pattern, response)
        
        if match:
            tool_name = match.group(1)
            params_str = match.group(2).strip()
            
            try:
                # 尝试解析JSON参数
                if params_str.startswith('{') and params_str.endswith('}'):
                    params = json.loads(params_str)
                else:
                    # 简单字符串参数
                    params = {"input": params_str}
                
                return {
                    "tool": tool_name,
                    "params": params
                }
            except json.JSONDecodeError:
                return {
                    "tool": tool_name,
                    "params": {"input": params_str}
                }
        
        return None
    
    def _execute_tool(self, tool_call: Dict[str, Any]) -> str:
        """执行工具调用"""
        tool_name = tool_call["tool"]
        params = tool_call["params"]
        
        if tool_name not in self.tools:
            return f"错误: 工具 '{tool_name}' 不存在"
        
        tool = self.tools[tool_name]
        
        try:
            # 根据参数类型调用工具
            if isinstance(params, dict):
                if "input" in params:
                    result = tool.execute(params["input"])
                else:
                    result = tool.execute(**params)
            else:
                result = tool.execute(params)
            
            return str(result)
        except Exception as e:
            return f"工具执行错误: {e}"
    
    def _add_to_memory(self, user_input: str, response: str):
        """添加到记忆"""
        self.memory.append({
            "timestamp": datetime.now().isoformat(),
            "user": user_input,
            "assistant": response
        })
        
        # 保持记忆长度限制
        if len(self.memory) > self.max_memory_length:
            self.memory.pop(0)
    
    def _get_memory_context(self) -> str:
        """获取记忆上下文"""
        if not self.memory:
            return ""
        
        context = "对话历史:\n"
        for item in self.memory[-5:]:  # 只使用最近5轮对话
            context += f"用户: {item['user']}\n"
            context += f"助手: {item['assistant']}\n\n"
        
        return context
    
    def process_request(self, user_input: str) -> str:
        """处理用户请求"""
        try:
            # 构建系统提示
            system_prompt = f"""你是一个智能助手，具有工具调用能力。

{self._get_tools_description()}

当需要使用工具时，请按以下格式回复：
使用工具: 工具名称
参数: 参数内容

如果不需要使用工具，直接回复用户即可。

{self._get_memory_context()}"""
            
            # 生成初始回复
            full_prompt = f"System: {system_prompt}\nUser: {user_input}\nAssistant: "
            response = self.model.generate_response(full_prompt)
            
            # 检查是否需要工具调用
            tool_call = self._parse_tool_call(response)
            
            if tool_call:
                # 执行工具调用
                tool_result = self._execute_tool(tool_call)
                
                # 基于工具结果生成最终回复
                final_prompt = f"""System: {system_prompt}
User: {user_input}
Assistant: {response}
工具执行结果: {tool_result}
请基于工具执行结果给出最终回复:
Assistant: """
                
                final_response = self.model.generate_response(final_prompt)
                
                # 添加到记忆
                self._add_to_memory(user_input, final_response)
                
                return final_response
            else:
                # 直接回复，添加到记忆
                self._add_to_memory(user_input, response)
                return response
                
        except Exception as e:
            error_msg = f"处理请求时发生错误: {e}"
            logger.error(error_msg)
            return error_msg
    
    def chat(self):
        """交互式对话"""
        print("DeepSeek智能体已启动！")
        print("输入 'quit' 退出，输入 'tools' 查看可用工具")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n用户: ").strip()
                
                if user_input.lower() == 'quit':
                    print("再见！")
                    break
                elif user_input.lower() == 'tools':
                    print(self._get_tools_description())
                    continue
                elif not user_input:
                    continue
                
                print("AI正在思考...")
                response = self.process_request(user_input)
                print(f"AI: {response}")
                
            except KeyboardInterrupt:
                print("\n\n再见！")
                break
            except Exception as e:
                print(f"发生错误: {e}")

def main():
    """主函数"""
    print("正在初始化DeepSeek智能体...")
    
    try:
        # 创建智能体实例
        agent = DeepSeekAgent()
        
        # 启动交互式对话
        agent.chat()
        
    except Exception as e:
        print(f"初始化失败: {e}")
        print("请确保已正确安装依赖并有足够的GPU内存")

if __name__ == "__main__":
    main()
