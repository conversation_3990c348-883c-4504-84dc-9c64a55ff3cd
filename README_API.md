# DeepSeek API智能体Agent部署指南

基于DeepSeek API的云端智能体Agent实现，无需本地GPU，支持工具调用、记忆系统和Web界面。

## 🚀 快速开始

### 方法1: 一键安装（推荐）

```bash
python setup_api_agent.py
```

### 方法2: 手动安装

#### 1. 获取DeepSeek API密钥

1. 访问 [DeepSeek平台](https://platform.deepseek.com/)
2. 注册账号并登录
3. 进入API管理页面
4. 创建新的API密钥
5. 复制API密钥（格式：sk-xxxxxx）

#### 2. 安装依赖

```bash
pip install -r requirements_api.txt
```

#### 3. 配置环境变量

创建 `.env` 文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置您的API密钥：
```
DEEPSEEK_API_KEY=sk-your_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-chat
```

#### 4. 启动智能体

```bash
# 命令行版本
python deepseek_api_agent.py

# Web界面版本（推荐）
streamlit run deepseek_web_agent.py

# 测试API连接
python deepseek_api_client.py
```

## 📋 系统要求

### 最低配置
- **Python**: 3.8+
- **内存**: 4GB
- **网络**: 稳定的互联网连接
- **API额度**: DeepSeek API调用额度

### 优势对比
| 特性 | 本地部署 | API调用 |
|------|----------|---------|
| GPU要求 | 需要高端GPU | 无需GPU |
| 内存要求 | 16GB+ | 4GB |
| 启动速度 | 慢（模型加载） | 快（秒级） |
| 运行成本 | 电费 | API费用 |
| 数据隐私 | 完全本地 | 云端处理 |
| 模型更新 | 手动 | 自动 |

## 🛠️ 功能特性

### 内置工具
- **🧮 计算器**: 安全的数学表达式计算
- **🌤️ 天气查询**: 城市天气信息（可扩展真实API）
- **🕒 时间工具**: 获取当前时间
- **🔍 网络搜索**: 信息搜索（可扩展真实搜索引擎）
- **📁 文件操作**: 保存和读取文本文件

### 界面选择
1. **命令行界面**: 简单直接的文本交互
2. **Web界面**: 美观的图形化界面，支持对话历史管理

## 💡 使用示例

### 基础对话
```
用户: 你好，请介绍一下你自己
AI: 你好！我是基于DeepSeek大模型的智能助手...
```

### 工具调用示例
```
用户: 帮我计算 (25 + 75) * 2
AI: 我来帮你计算这个表达式。
[调用计算器工具]
计算结果: (25 + 75) * 2 = 200

用户: 现在几点了？
AI: [调用时间工具]
当前时间: 2024年12月19日 14:30:25

用户: 查询北京的天气
AI: [调用天气工具]
北京的天气: 晴天，温度15°C
```

## 🔧 自定义开发

### 添加新工具

在 `deepseek_api_agent.py` 中添加：

```python
def my_custom_tool(param1: str, param2: int) -> str:
    """自定义工具功能"""
    # 实现你的工具逻辑
    return f"处理结果: {param1} - {param2}"

# 注册工具
agent.register_tool(
    name="my_tool",
    description="我的自定义工具",
    func=my_custom_tool,
    parameters={
        "type": "object",
        "properties": {
            "param1": {"type": "string", "description": "参数1"},
            "param2": {"type": "integer", "description": "参数2"}
        },
        "required": ["param1", "param2"]
    }
)
```

### 集成外部API

```python
import requests

def real_weather_tool(city: str) -> str:
    """真实天气API集成"""
    api_key = os.getenv("WEATHER_API_KEY")
    url = f"http://api.openweathermap.org/data/2.5/weather"
    params = {
        "q": city,
        "appid": api_key,
        "units": "metric",
        "lang": "zh_cn"
    }
    
    try:
        response = requests.get(url, params=params)
        data = response.json()
        
        if response.status_code == 200:
            weather = data["weather"][0]["description"]
            temp = data["main"]["temp"]
            return f"{city}的天气: {weather}, 温度: {temp}°C"
        else:
            return f"获取{city}天气失败: {data.get('message', '未知错误')}"
    except Exception as e:
        return f"天气查询错误: {e}"
```

### 自定义系统提示

```python
agent.set_system_prompt("""
你是一个专业的编程助手，专门帮助用户解决编程问题。
你可以：
1. 解答编程相关问题
2. 提供代码示例
3. 调用工具执行计算和搜索
4. 帮助调试代码

请始终保持专业和友好的态度。
""")
```

## 📊 成本估算

### DeepSeek API定价（参考）
- **输入Token**: ¥0.0014/1K tokens
- **输出Token**: ¥0.0028/1K tokens

### 使用成本示例
- **简单对话** (100字): ~¥0.001
- **复杂任务** (1000字): ~¥0.01
- **日常使用** (1万字/天): ~¥0.1

## 🐛 常见问题

### Q: API密钥无效
**A**: 检查密钥格式和有效性
```bash
# 测试API连接
python deepseek_api_client.py
```

### Q: 网络连接失败
**A**: 检查网络和防火墙设置
```python
# 设置代理（如需要）
import os
os.environ["HTTP_PROXY"] = "http://proxy:port"
os.environ["HTTPS_PROXY"] = "http://proxy:port"
```

### Q: 工具调用不生效
**A**: 检查工具参数格式
```python
# 确保参数格式正确
parameters = {
    "type": "object",
    "properties": {
        "param_name": {
            "type": "string",
            "description": "参数描述"
        }
    },
    "required": ["param_name"]
}
```

### Q: Web界面启动失败
**A**: 检查Streamlit安装
```bash
pip install streamlit --upgrade
streamlit --version
```

## 🔄 更新和维护

### 更新依赖
```bash
pip install -r requirements_api.txt --upgrade
```

### 检查API状态
```bash
python deepseek_api_client.py
```

### 备份配置
```bash
cp .env .env.backup
```

## 🌟 高级功能

### 1. 多智能体协作
```python
# 创建专门的智能体
code_agent = DeepSeekAPIAgent(model="deepseek-coder")
chat_agent = DeepSeekAPIAgent(model="deepseek-chat")

# 根据任务类型选择智能体
if "代码" in user_input:
    response = code_agent.process_message(user_input)
else:
    response = chat_agent.process_message(user_input)
```

### 2. 流式输出
```python
for chunk in client.chat_completion_stream(messages):
    print(chunk, end="", flush=True)
```

### 3. 批量处理
```python
tasks = ["任务1", "任务2", "任务3"]
results = []

for task in tasks:
    result = agent.process_message(task)
    results.append(result)
```

## 📚 学习资源

- [DeepSeek官方文档](https://api-docs.deepseek.com/)
- [OpenAI API文档](https://platform.openai.com/docs/api-reference)
- [Streamlit文档](https://docs.streamlit.io/)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License - 详见LICENSE文件

---

**开始您的DeepSeek API智能体之旅！** 🎉
