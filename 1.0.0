Collecting openai
  Using cached openai-1.97.0-py3-none-any.whl.metadata (29 kB)
Collecting anyio<5,>=3.5.0 (from openai)
  Using cached anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)
Collecting distro<2,>=1.7.0 (from openai)
  Using cached distro-1.9.0-py3-none-any.whl.metadata (6.8 kB)
Collecting httpx<1,>=0.23.0 (from openai)
  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting jiter<1,>=0.4.0 (from openai)
  Using cached jiter-0.10.0-cp311-cp311-win_amd64.whl.metadata (5.3 kB)
Collecting pydantic<3,>=1.9.0 (from openai)
  Downloading pydantic-2.11.7-py3-none-any.whl.metadata (67 kB)
     --------------------------------------- 68.0/68.0 kB 36.6 kB/s eta 0:00:00
Collecting sniffio (from openai)
  Downloading sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting tqdm>4 (from openai)
  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
     ---------------------------------------- 57.7/57.7 kB 7.1 kB/s eta 0:00:00
Collecting typing-extensions<5,>=4.11 (from openai)
  Downloading typing_extensions-4.14.1-py3-none-any.whl.metadata (3.0 kB)
Requirement already satisfied: idna>=2.8 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.11_qbz5n2kfra8p0\localcache\local-packages\python311\site-packages (from anyio<5,>=3.5.0->openai) (3.10)
Requirement already satisfied: certifi in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.11_qbz5n2kfra8p0\localcache\local-packages\python311\site-packages (from httpx<1,>=0.23.0->openai) (2025.7.14)
Collecting httpcore==1.* (from httpx<1,>=0.23.0->openai)
  Downloading httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Collecting h11>=0.16 (from httpcore==1.*->httpx<1,>=0.23.0->openai)
  Downloading h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Collecting annotated-types>=0.6.0 (from pydantic<3,>=1.9.0->openai)
  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.33.2 (from pydantic<3,>=1.9.0->openai)
  Downloading pydantic_core-2.33.2-cp311-cp311-win_amd64.whl.metadata (6.9 kB)
Collecting typing-inspection>=0.4.0 (from pydantic<3,>=1.9.0->openai)
  Downloading typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.11_qbz5n2kfra8p0\localcache\local-packages\python311\site-packages (from tqdm>4->openai) (0.4.6)
Downloading openai-1.97.0-py3-none-any.whl (764 kB)
   ---------------------------------------- 765.0/765.0 kB 6.6 kB/s eta 0:00:00
Downloading anyio-4.9.0-py3-none-any.whl (100 kB)
   ---------------------------------------- 100.9/100.9 kB 5.0 kB/s eta 0:00:00
Downloading distro-1.9.0-py3-none-any.whl (20 kB)
Downloading httpx-0.28.1-py3-none-any.whl (73 kB)
   ---------------------------------------- 73.5/73.5 kB 5.5 kB/s eta 0:00:00
Downloading httpcore-1.0.9-py3-none-any.whl (78 kB)
   ---------------------------------------- 78.8/78.8 kB 5.4 kB/s eta 0:00:00
Downloading jiter-0.10.0-cp311-cp311-win_amd64.whl (209 kB)
   ---------------------------------------- 209.2/209.2 kB 7.8 kB/s eta 0:00:00
Downloading pydantic-2.11.7-py3-none-any.whl (444 kB)
   ---------------------------------------- 444.8/444.8 kB 8.0 kB/s eta 0:00:00
Downloading pydantic_core-2.33.2-cp311-cp311-win_amd64.whl (2.0 MB)
   ---------------------------------------- 2.0/2.0 MB 10.4 kB/s eta 0:00:00
Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)
   ---------------------------------------- 78.5/78.5 kB 5.4 kB/s eta 0:00:00
Downloading typing_extensions-4.14.1-py3-none-any.whl (43 kB)
   ---------------------------------------- 43.9/43.9 kB 10.8 kB/s eta 0:00:00
Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Downloading h11-0.16.0-py3-none-any.whl (37 kB)
Installing collected packages: typing-extensions, tqdm, sniffio, jiter, h11, distro, annotated-types, typing-inspection, pydantic-core, httpcore, anyio, pydantic, httpx, openai
Successfully installed annotated-types-0.7.0 anyio-4.9.0 distro-1.9.0 h11-0.16.0 httpcore-1.0.9 httpx-0.28.1 jiter-0.10.0 openai-1.97.0 pydantic-2.11.7 pydantic-core-2.33.2 sniffio-1.3.1 tqdm-4.67.1 typing-extensions-4.14.1 typing-inspection-0.4.1
