#!/usr/bin/env python3
"""
DeepSeek API智能体一键安装和启动脚本
"""

import subprocess
import sys
import os
import platform

def run_command(command, description=""):
    """运行命令并显示结果"""
    print(f"🔄 {description}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout:
                print(f"输出: {result.stdout}")
        else:
            print(f"❌ {description} 失败")
            print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    
    return True

def check_python():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor} 符合要求")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.8或更高版本")
        return False

def install_dependencies():
    """安装Python依赖"""
    print("📦 安装Python依赖...")
    
    if not run_command("pip install -r requirements_api.txt", "安装依赖包"):
        print("⚠️ 使用备用安装方法...")
        dependencies = [
            "openai>=1.0.0",
            "requests>=2.28.0", 
            "python-dotenv>=0.19.0",
            "streamlit>=1.28.0",
            "gradio>=3.50.0"
        ]
        
        for dep in dependencies:
            run_command(f"pip install {dep}", f"安装 {dep}")
    
    return True

def setup_environment():
    """设置环境配置"""
    print("⚙️ 设置环境配置...")
    
    # 检查.env文件是否存在
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            # 复制示例文件
            if platform.system() == "Windows":
                run_command("copy .env.example .env", "创建环境配置文件")
            else:
                run_command("cp .env.example .env", "创建环境配置文件")
        else:
            # 创建基本的.env文件
            with open(".env", "w") as f:
                f.write("# DeepSeek API配置\n")
                f.write("DEEPSEEK_API_KEY=your_deepseek_api_key_here\n")
                f.write("DEEPSEEK_BASE_URL=https://api.deepseek.com\n")
                f.write("DEEPSEEK_MODEL=deepseek-chat\n")
        
        print("✅ 环境配置文件已创建")
        print("📝 请编辑 .env 文件，设置您的DEEPSEEK_API_KEY")
    else:
        print("✅ 环境配置文件已存在")

def get_api_key():
    """获取API密钥"""
    print("\n" + "="*60)
    print("🔑 API密钥配置")
    print("="*60)
    
    # 检查环境变量
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if api_key and api_key != "your_deepseek_api_key_here":
        print("✅ 检测到已配置的API密钥")
        return api_key
    
    print("📋 获取DeepSeek API密钥的步骤:")
    print("1. 访问 https://platform.deepseek.com/")
    print("2. 注册账号并登录")
    print("3. 进入API管理页面")
    print("4. 创建新的API密钥")
    print("5. 复制API密钥")
    
    while True:
        api_key = input("\n请输入您的DeepSeek API密钥 (或按回车跳过): ").strip()
        
        if not api_key:
            print("⚠️ 跳过API密钥配置，您可以稍后在.env文件中设置")
            return None
        
        if api_key.startswith("sk-"):
            # 保存到.env文件
            try:
                with open(".env", "r") as f:
                    content = f.read()
                
                # 替换API密钥
                if "DEEPSEEK_API_KEY=" in content:
                    lines = content.split("\n")
                    for i, line in enumerate(lines):
                        if line.startswith("DEEPSEEK_API_KEY="):
                            lines[i] = f"DEEPSEEK_API_KEY={api_key}"
                            break
                    content = "\n".join(lines)
                else:
                    content += f"\nDEEPSEEK_API_KEY={api_key}\n"
                
                with open(".env", "w") as f:
                    f.write(content)
                
                print("✅ API密钥已保存到.env文件")
                return api_key
                
            except Exception as e:
                print(f"❌ 保存API密钥失败: {e}")
                return api_key
        else:
            print("❌ API密钥格式不正确，应该以'sk-'开头")

def test_api_connection(api_key):
    """测试API连接"""
    if not api_key:
        print("⚠️ 未配置API密钥，跳过连接测试")
        return False
    
    print("🔗 测试API连接...")
    
    try:
        os.environ["DEEPSEEK_API_KEY"] = api_key
        from deepseek_api_client import DeepSeekAPIClient
        
        client = DeepSeekAPIClient(api_key)
        response = client.simple_chat("你好")
        
        print("✅ API连接测试成功！")
        print(f"测试回复: {response[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        print("请检查:")
        print("1. API密钥是否正确")
        print("2. 网络连接是否正常")
        print("3. API额度是否充足")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("🎉 安装完成！使用指南")
    print("="*60)
    
    print("\n📖 启动方式:")
    print("1. 命令行版本:")
    print("   python deepseek_api_agent.py")
    
    print("\n2. Web界面版本 (推荐):")
    print("   streamlit run deepseek_web_agent.py")
    
    print("\n3. 测试API客户端:")
    print("   python deepseek_api_client.py")
    
    print("\n🛠️ 功能特性:")
    print("- 智能对话和问答")
    print("- 自动工具调用 (计算器、天气、搜索等)")
    print("- 对话历史记忆")
    print("- Web图形界面")
    print("- 可扩展的工具系统")
    
    print("\n💡 使用示例:")
    print("- '帮我计算 (100 + 200) * 3'")
    print("- '现在几点了？'")
    print("- '查询北京的天气'")
    print("- '搜索Python教程'")
    
    print("\n📝 配置文件:")
    print("- .env: 环境变量配置")
    print("- requirements_api.txt: Python依赖")

def main():
    """主安装流程"""
    print("🎯 DeepSeek API智能体安装向导")
    print("=" * 60)
    
    # 1. 检查Python版本
    if not check_python():
        return
    
    # 2. 安装依赖
    print("\n" + "=" * 60)
    install_dependencies()
    
    # 3. 设置环境
    print("\n" + "=" * 60)
    setup_environment()
    
    # 4. 获取API密钥
    api_key = get_api_key()
    
    # 5. 测试API连接
    if api_key:
        print("\n" + "=" * 60)
        test_api_connection(api_key)
    
    # 6. 显示使用指南
    show_usage_guide()
    
    # 7. 询问是否立即启动
    print("\n" + "=" * 60)
    choice = input("是否立即启动Web界面版本？(y/n): ").lower()
    if choice == 'y':
        print("🚀 启动Web界面...")
        try:
            subprocess.run([sys.executable, "-m", "streamlit", "run", "deepseek_web_agent.py"])
        except KeyboardInterrupt:
            print("\n👋 再见！")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            print("请手动运行: streamlit run deepseek_web_agent.py")

if __name__ == "__main__":
    main()
