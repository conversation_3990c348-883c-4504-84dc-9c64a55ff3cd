#!/usr/bin/env python3
"""
基于DeepSeek API的智能体Agent
支持工具调用、记忆系统和任务执行
"""

import json
import re
import logging
import requests
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import os
import math

from deepseek_api_client import DeepSeekAPIClient

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Tool:
    """工具类"""
    def __init__(self, name: str, description: str, func: Callable, parameters: Dict[str, Any] = None):
        self.name = name
        self.description = description
        self.func = func
        self.parameters = parameters or {}
    
    def execute(self, **kwargs) -> str:
        """执行工具"""
        try:
            return str(self.func(**kwargs))
        except Exception as e:
            return f"工具执行错误: {e}"
    
    def to_openai_format(self) -> Dict[str, Any]:
        """转换为OpenAI函数调用格式"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters
            }
        }

class DeepSeekAPIAgent:
    """基于DeepSeek API的智能体"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "deepseek-chat"):
        """
        初始化智能体
        
        Args:
            api_key: DeepSeek API密钥
            model: 使用的模型名称
        """
        self.client = DeepSeekAPIClient(api_key)
        self.model = model
        self.tools = {}
        self.conversation_history = []
        self.max_history_length = 20
        self.system_prompt = "你是一个智能助手，可以使用各种工具来帮助用户完成任务。"
        
        # 注册默认工具
        self._register_default_tools()
        
        logger.info(f"DeepSeek API智能体初始化成功，使用模型: {model}")
    
    def _register_default_tools(self):
        """注册默认工具"""
        
        # 计算器工具
        def calculator(expression: str) -> str:
            """安全的数学计算器"""
            try:
                # 只允许安全的数学运算
                allowed_chars = set('0123456789+-*/.() ')
                if all(c in allowed_chars for c in expression):
                    result = eval(expression)
                    return f"计算结果: {expression} = {result}"
                else:
                    return "错误: 表达式包含不允许的字符"
            except Exception as e:
                return f"计算错误: {e}"
        
        # 天气查询工具（模拟）
        def get_weather(city: str) -> str:
            """获取天气信息"""
            # 这里可以集成真实的天气API
            weather_data = {
                "北京": "晴天，温度15°C",
                "上海": "多云，温度18°C", 
                "广州": "小雨，温度22°C",
                "深圳": "晴天，温度25°C"
            }
            return weather_data.get(city, f"{city}的天气信息暂时无法获取")
        
        # 时间工具
        def get_current_time() -> str:
            """获取当前时间"""
            return datetime.now().strftime("当前时间: %Y年%m月%d日 %H:%M:%S")
        
        # 搜索工具（模拟）
        def web_search(query: str) -> str:
            """网络搜索"""
            return f"搜索'{query}'的结果: 这是关于{query}的相关信息..."
        
        # 文件操作工具
        def save_text_file(filename: str, content: str) -> str:
            """保存文本文件"""
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                return f"文件 {filename} 保存成功"
            except Exception as e:
                return f"保存文件失败: {e}"
        
        # 注册工具
        self.register_tool(
            name="calculator",
            description="执行数学计算",
            func=calculator,
            parameters={
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "要计算的数学表达式"
                    }
                },
                "required": ["expression"]
            }
        )
        
        self.register_tool(
            name="get_weather",
            description="获取指定城市的天气信息",
            func=get_weather,
            parameters={
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "城市名称"
                    }
                },
                "required": ["city"]
            }
        )
        
        self.register_tool(
            name="get_current_time",
            description="获取当前时间",
            func=get_current_time,
            parameters={"type": "object", "properties": {}}
        )
        
        self.register_tool(
            name="web_search",
            description="在网络上搜索信息",
            func=web_search,
            parameters={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "搜索关键词"
                    }
                },
                "required": ["query"]
            }
        )
        
        self.register_tool(
            name="save_text_file",
            description="保存文本内容到文件",
            func=save_text_file,
            parameters={
                "type": "object",
                "properties": {
                    "filename": {
                        "type": "string",
                        "description": "文件名"
                    },
                    "content": {
                        "type": "string",
                        "description": "文件内容"
                    }
                },
                "required": ["filename", "content"]
            }
        )
    
    def register_tool(self, name: str, description: str, func: Callable, parameters: Dict[str, Any]):
        """注册新工具"""
        tool = Tool(name, description, func, parameters)
        self.tools[name] = tool
        logger.info(f"工具 '{name}' 注册成功")
    
    def set_system_prompt(self, prompt: str):
        """设置系统提示"""
        self.system_prompt = prompt
    
    def _build_messages(self, user_input: str) -> List[Dict[str, str]]:
        """构建消息列表"""
        messages = [{"role": "system", "content": self.system_prompt}]
        
        # 添加历史对话
        for item in self.conversation_history[-10:]:  # 只保留最近10轮对话
            messages.append({"role": "user", "content": item["user"]})
            messages.append({"role": "assistant", "content": item["assistant"]})
        
        # 添加当前用户输入
        messages.append({"role": "user", "content": user_input})
        
        return messages
    
    def _execute_function_call(self, function_call) -> str:
        """执行函数调用"""
        function_name = function_call.name
        arguments = json.loads(function_call.arguments)
        
        if function_name not in self.tools:
            return f"错误: 工具 '{function_name}' 不存在"
        
        tool = self.tools[function_name]
        return tool.execute(**arguments)
    
    def process_message(self, user_input: str) -> str:
        """处理用户消息"""
        try:
            messages = self._build_messages(user_input)
            
            # 准备工具列表
            tools = [tool.to_openai_format() for tool in self.tools.values()]
            
            # 调用API
            response = self.client.client.chat.completions.create(
                model=self.model,
                messages=messages,
                tools=tools,
                tool_choice="auto",
                temperature=0.7,
                max_tokens=2048
            )
            
            message = response.choices[0].message
            
            # 检查是否有工具调用
            if message.tool_calls:
                # 执行工具调用
                tool_results = []
                for tool_call in message.tool_calls:
                    result = self._execute_function_call(tool_call.function)
                    tool_results.append(f"工具 {tool_call.function.name} 执行结果: {result}")
                
                # 将工具结果添加到消息中，重新调用API获取最终回复
                messages.append({
                    "role": "assistant",
                    "content": message.content or "",
                    "tool_calls": [{"id": tc.id, "type": tc.type, "function": {"name": tc.function.name, "arguments": tc.function.arguments}} for tc in message.tool_calls]
                })

                for i, tool_call in enumerate(message.tool_calls):
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": tool_results[i]
                    })
                
                # 重新调用API获取最终回复
                final_response = self.client.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=0.7,
                    max_tokens=2048
                )
                
                final_content = final_response.choices[0].message.content
            else:
                final_content = message.content
            
            # 保存到对话历史
            self._add_to_history(user_input, final_content)
            
            return final_content
            
        except Exception as e:
            error_msg = f"处理消息时发生错误: {e}"
            logger.error(error_msg)
            return error_msg
    
    def _add_to_history(self, user_input: str, assistant_response: str):
        """添加到对话历史"""
        self.conversation_history.append({
            "user": user_input,
            "assistant": assistant_response,
            "timestamp": datetime.now().isoformat()
        })
        
        # 保持历史长度限制
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history.pop(0)
    
    def get_tools_info(self) -> str:
        """获取工具信息"""
        if not self.tools:
            return "当前没有可用工具"
        
        info = "可用工具:\n"
        for tool in self.tools.values():
            info += f"- {tool.name}: {tool.description}\n"
        
        return info
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        logger.info("对话历史已清空")
    
    def chat(self):
        """交互式对话"""
        print("🤖 DeepSeek API智能体已启动！")
        print("💡 输入 'help' 查看帮助，输入 'quit' 退出")
        print("🔧 输入 'tools' 查看可用工具")
        print("🗑️ 输入 'clear' 清空对话历史")
        print("-" * 60)
        
        while True:
            try:
                user_input = input("\n👤 用户: ").strip()
                
                if user_input.lower() == 'quit':
                    print("👋 再见！")
                    break
                elif user_input.lower() == 'help':
                    print("📖 帮助信息:")
                    print("- 直接输入问题与AI对话")
                    print("- AI会自动调用合适的工具")
                    print("- 输入 'tools' 查看可用工具")
                    print("- 输入 'clear' 清空对话历史")
                    print("- 输入 'quit' 退出程序")
                    continue
                elif user_input.lower() == 'tools':
                    print(self.get_tools_info())
                    continue
                elif user_input.lower() == 'clear':
                    self.clear_history()
                    print("✅ 对话历史已清空")
                    continue
                elif not user_input:
                    continue
                
                print("🤔 AI正在思考...")
                response = self.process_message(user_input)
                print(f"🤖 AI: {response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    print("🚀 正在启动DeepSeek API智能体...")
    
    try:
        # 检查API密钥
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            print("❌ 请设置DEEPSEEK_API_KEY环境变量")
            print("📝 在 .env 文件中添加: DEEPSEEK_API_KEY=your_api_key_here")
            return
        
        # 创建智能体
        agent = DeepSeekAPIAgent()
        
        # 开始对话
        agent.chat()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("📋 请检查:")
        print("1. DEEPSEEK_API_KEY环境变量是否设置正确")
        print("2. 网络连接是否正常")
        print("3. API密钥是否有效")

if __name__ == "__main__":
    main()
